import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fujimo/global/precision_ruler_widget.dart';

void main() {
  group('PrecisionRuler Widget Tests', () {
    testWidgets('should position startWidget and endWidget correctly when movePointer is false', (WidgetTester tester) async {
      const startWidget = Icon(Icons.remove, key: Key('start_widget'));
      const endWidget = Icon(Icons.add, key: Key('end_widget'));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 300,
              height: 60,
              child: PrecisionRuler(
                value: 0.0,
                movePointer: false, // Moving ruler with fixed pointer
                startWidget: startWidget,
                endWidget: endWidget,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Verify that both widgets are present
      expect(find.byKey(const Key('start_widget')), findsOneWidget);
      expect(find.by<PERSON>ey(const Key('end_widget')), findsOneWidget);
      
      // In movePointer false mode, widgets should be positioned with padding from edges
      final startWidgetFinder = find.byKey(const Key('start_widget'));
      final endWidgetFinder = find.byKey(const Key('end_widget'));
      
      expect(startWidgetFinder, findsOneWidget);
      expect(endWidgetFinder, findsOneWidget);
    });

    testWidgets('should position startWidget and endWidget correctly when movePointer is true', (WidgetTester tester) async {
      const startWidget = Icon(Icons.remove, key: Key('start_widget'));
      const endWidget = Icon(Icons.add, key: Key('end_widget'));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 300,
              height: 60,
              child: PrecisionRuler(
                value: 0.0,
                movePointer: true, // Fixed ruler with moving pointer
                startWidget: startWidget,
                endWidget: endWidget,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Verify that both widgets are present
      expect(find.byKey(const Key('start_widget')), findsOneWidget);
      expect(find.byKey(const Key('end_widget')), findsOneWidget);
      
      // In movePointer true mode, widgets should be positioned at the very edges
      // without overlapping the ruler content
      final startWidgetFinder = find.byKey(const Key('start_widget'));
      final endWidgetFinder = find.byKey(const Key('end_widget'));
      
      expect(startWidgetFinder, findsOneWidget);
      expect(endWidgetFinder, findsOneWidget);
    });

    testWidgets('should handle null startWidget and endWidget gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 300,
              height: 60,
              child: PrecisionRuler(
                value: 0.0,
                movePointer: true,
                startWidget: null,
                endWidget: null,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Should not crash and should render the ruler
      expect(find.byType(PrecisionRuler), findsOneWidget);
    });

    testWidgets('should respond to value changes', (WidgetTester tester) async {
      double currentValue = 0.0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Container(
                  width: 300,
                  height: 60,
                  child: PrecisionRuler(
                    value: currentValue,
                    movePointer: true,
                    onChanged: (value) {
                      setState(() {
                        currentValue = value;
                      });
                    },
                  ),
                );
              },
            ),
          ),
        ),
      );

      // Verify initial state
      expect(find.byType(PrecisionRuler), findsOneWidget);
      
      // The widget should render without errors
      await tester.pumpAndSettle();
    });

    testWidgets('should work with RTL text direction', (WidgetTester tester) async {
      const startWidget = Icon(Icons.remove, key: Key('start_widget'));
      const endWidget = Icon(Icons.add, key: Key('end_widget'));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Directionality(
            textDirection: TextDirection.rtl,
            child: Scaffold(
              body: Container(
                width: 300,
                height: 60,
                child: PrecisionRuler(
                  value: 0.0,
                  movePointer: true,
                  startWidget: startWidget,
                  endWidget: endWidget,
                  onChanged: (value) {},
                ),
              ),
            ),
          ),
        ),
      );

      // Verify that both widgets are present in RTL mode
      expect(find.byKey(const Key('start_widget')), findsOneWidget);
      expect(find.byKey(const Key('end_widget')), findsOneWidget);
    });
  });
}
